"""
Test Configuration
==================

This module provides test fixtures and configuration for the test suite.
"""

import pytest
from unittest.mock import Mock
from fastapi.testclient import TestClient

# Import the app and dependencies
from app.main import app
import app.dependencies as deps


@pytest.fixture(autouse=True)
def setup_test_dependencies():
    """
    Automatically setup mock dependencies for all tests.
    This fixture runs before each test and ensures we have a mock client.
    """
    # Create a mock recoater client
    mock_client = Mock()

    # Set up default return values for common methods
    mock_client.get_state.return_value = {"status": "idle", "timestamp": "2024-01-01T00:00:00Z"}
    mock_client.get_config.return_value = {"setting1": "value1"}
    mock_client.get_drums.return_value = [{"id": 0, "status": "ready"}]
    mock_client.health_check.return_value = True

    # Axis methods (for MockRecoaterClient compatibility)
    mock_client.get_axis_status.return_value = {"position": 0.0, "running": False, "homed": True}
    mock_client.move_axis.return_value = {"command_id": "motion_123"}
    mock_client.home_axis.return_value = {"command_id": "home_123"}
    mock_client.get_axis_motion.return_value = {"mode": "idle"}
    mock_client.cancel_axis_motion.return_value = {"success": True}
    mock_client.set_gripper_state.return_value = {"success": True}
    mock_client.get_gripper_state.return_value = {"enabled": False}

    # Drum control methods
    mock_client.get_drum_motion.return_value = {"mode": "idle", "speed": 0.0}
    mock_client.set_drum_motion.return_value = {"command_id": "drum_motion_123"}
    mock_client.cancel_drum_motion.return_value = {"success": True}
    mock_client.get_drum_ejection.return_value = {"target": 0.0, "unit": "pascal"}
    mock_client.set_drum_ejection.return_value = {"success": True}
    mock_client.get_drum_suction.return_value = {"target": 0.0, "unit": "pascal"}
    mock_client.set_drum_suction.return_value = {"success": True}

    # Leveler control methods
    mock_client.get_leveler_pressure.return_value = {"maximum": 10.0, "target": 5.0, "value": 4.8}
    mock_client.set_leveler_pressure.return_value = {"success": True, "target_pressure": 5.0, "unit": "Pa"}
    mock_client.get_leveler_sensor.return_value = {"state": True}

    # Print control methods
    mock_client.get_layer_parameters.return_value = {
        "filling_id": 1,
        "speed": 30.0,
        "powder_saving": True,
        "x_offset": 0.0,
        "max_x_offset": 100.0
    }
    mock_client.set_layer_parameters.return_value = {"success": True}
    mock_client.get_layer_preview.return_value = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    mock_client.start_print_job.return_value = {"success": True, "job_id": "job_12345", "status": "started"}
    mock_client.cancel_print_job.return_value = {"success": True, "status": "cancelled"}

    # Replace the global client in the dependencies module
    original_client = deps._recoater_client
    deps._recoater_client = mock_client

    # Override the dependency function to return our mock
    app.dependency_overrides[deps.get_recoater_client] = lambda: mock_client

    yield mock_client

    # Restore the original client after the test
    deps._recoater_client = original_client
    app.dependency_overrides.clear()


@pytest.fixture
def client():
    """
    Provide a test client for making HTTP requests.
    """
    return TestClient(app)


@pytest.fixture
def mock_recoater_client(setup_test_dependencies):
    """
    Provide access to the mock recoater client for test customization.
    """
    return setup_test_dependencies
