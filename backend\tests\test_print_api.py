"""
Tests for Print API endpoints
============================

This module contains comprehensive tests for the print control API endpoints
including layer parameters and preview functionality.
"""

import pytest
from unittest.mock import Mock
from fastapi.testclient import TestClient
from io import BytesIO

from app.main import app
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError


class TestLayerParametersEndpoints:
    """Test layer parameters endpoints."""

    def test_get_layer_parameters_success(self, client, mock_recoater_client):
        """Test successful layer parameters retrieval."""
        # Mock successful response
        mock_recoater_client.get_layer_parameters.return_value = {
            "filling_id": 1,
            "speed": 30.0,
            "powder_saving": True,
            "x_offset": 0.0,
            "max_x_offset": 100.0
        }

        response = client.get("/api/v1/print/layer/parameters")

        assert response.status_code == 200
        data = response.json()
        assert data["filling_id"] == 1
        assert data["speed"] == 30.0
        assert data["powder_saving"] is True
        assert data["x_offset"] == 0.0
        assert data["max_x_offset"] == 100.0
        mock_recoater_client.get_layer_parameters.assert_called_once()

    def test_get_layer_parameters_connection_error(self, client, mock_recoater_client):
        """Test layer parameters retrieval with connection error."""
        mock_recoater_client.get_layer_parameters.side_effect = RecoaterConnectionError("Connection failed")

        response = client.get("/api/v1/print/layer/parameters")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_get_layer_parameters_api_error(self, client, mock_recoater_client):
        """Test layer parameters retrieval with API error."""
        mock_recoater_client.get_layer_parameters.side_effect = RecoaterAPIError("API error")

        response = client.get("/api/v1/print/layer/parameters")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]

    def test_set_layer_parameters_success(self, client, mock_recoater_client):
        """Test successful layer parameters setting."""
        mock_recoater_client.set_layer_parameters.return_value = {"success": True}

        request_data = {
            "filling_id": 2,
            "speed": 25.0,
            "powder_saving": False,
            "x_offset": 5.0
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Layer parameters set successfully"
        assert data["parameters"]["filling_id"] == 2
        assert data["parameters"]["speed"] == 25.0
        assert data["parameters"]["powder_saving"] is False
        assert data["parameters"]["x_offset"] == 5.0

        mock_recoater_client.set_layer_parameters.assert_called_once_with(
            filling_id=2,
            speed=25.0,
            powder_saving=False,
            x_offset=5.0
        )

    def test_set_layer_parameters_minimal(self, client, mock_recoater_client):
        """Test setting layer parameters with minimal required fields."""
        mock_recoater_client.set_layer_parameters.return_value = {"success": True}

        request_data = {
            "filling_id": 1,
            "speed": 30.0
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["parameters"]["filling_id"] == 1
        assert data["parameters"]["speed"] == 30.0
        assert data["parameters"]["powder_saving"] is True  # Default value
        assert data["parameters"]["x_offset"] is None

        mock_recoater_client.set_layer_parameters.assert_called_once_with(
            filling_id=1,
            speed=30.0,
            powder_saving=True,
            x_offset=None
        )

    def test_set_layer_parameters_validation_error(self, client, mock_recoater_client):
        """Test layer parameters setting with validation error."""
        request_data = {
            "filling_id": 1,
            "speed": -5.0  # Invalid negative speed
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 422  # Validation error
        mock_recoater_client.set_layer_parameters.assert_not_called()

    def test_set_layer_parameters_connection_error(self, client, mock_recoater_client):
        """Test layer parameters setting with connection error."""
        mock_recoater_client.set_layer_parameters.side_effect = RecoaterConnectionError("Connection failed")

        request_data = {
            "filling_id": 1,
            "speed": 30.0
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_set_layer_parameters_api_error(self, client, mock_recoater_client):
        """Test layer parameters setting with API error."""
        mock_recoater_client.set_layer_parameters.side_effect = RecoaterAPIError("API error")

        request_data = {
            "filling_id": 1,
            "speed": 30.0
        }

        response = client.put("/api/v1/print/layer/parameters", json=request_data)

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]


class TestLayerPreviewEndpoint:
    """Test layer preview endpoint."""

    def test_get_layer_preview_success(self, client, mock_recoater_client):
        """Test successful layer preview retrieval."""
        # Mock PNG image data
        mock_png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
        mock_recoater_client.get_layer_preview.return_value = mock_png_data

        response = client.get("/api/v1/print/layer/preview")

        assert response.status_code == 200
        assert response.headers["content-type"] == "image/png"
        assert "layer_preview.png" in response.headers.get("content-disposition", "")
        assert response.content == mock_png_data
        mock_recoater_client.get_layer_preview.assert_called_once()

    def test_get_layer_preview_connection_error(self, client, mock_recoater_client):
        """Test layer preview retrieval with connection error."""
        mock_recoater_client.get_layer_preview.side_effect = RecoaterConnectionError("Connection failed")

        response = client.get("/api/v1/print/layer/preview")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_get_layer_preview_api_error(self, client, mock_recoater_client):
        """Test layer preview retrieval with API error."""
        mock_recoater_client.get_layer_preview.side_effect = RecoaterAPIError("API error")

        response = client.get("/api/v1/print/layer/preview")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]


class TestPrintJobEndpoints:
    """Test print job management endpoints."""

    def test_start_print_job_success(self, client, mock_recoater_client):
        """Test successful print job start."""
        mock_recoater_client.start_print_job.return_value = {
            "success": True,
            "job_id": "job_12345",
            "status": "started"
        }

        response = client.post("/api/v1/print/job")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["status"] == "started"
        assert data["job_id"] == "job_12345"
        mock_recoater_client.start_print_job.assert_called_once()

    def test_start_print_job_connection_error(self, client, mock_recoater_client):
        """Test print job start with connection error."""
        mock_recoater_client.start_print_job.side_effect = RecoaterConnectionError("Connection failed")

        response = client.post("/api/v1/print/job")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_start_print_job_api_error(self, client, mock_recoater_client):
        """Test print job start with API error (conflict)."""
        mock_recoater_client.start_print_job.side_effect = RecoaterAPIError("Already printing")

        response = client.post("/api/v1/print/job")

        assert response.status_code == 409
        assert "Cannot start print job" in response.json()["detail"]

    def test_cancel_print_job_success(self, client, mock_recoater_client):
        """Test successful print job cancellation."""
        mock_recoater_client.cancel_print_job.return_value = {
            "success": True,
            "status": "cancelled"
        }

        response = client.delete("/api/v1/print/job")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["status"] == "cancelled"
        assert data["job_id"] is None
        mock_recoater_client.cancel_print_job.assert_called_once()

    def test_cancel_print_job_connection_error(self, client, mock_recoater_client):
        """Test print job cancellation with connection error."""
        mock_recoater_client.cancel_print_job.side_effect = RecoaterConnectionError("Connection failed")

        response = client.delete("/api/v1/print/job")

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    def test_cancel_print_job_api_error(self, client, mock_recoater_client):
        """Test print job cancellation with API error."""
        mock_recoater_client.cancel_print_job.side_effect = RecoaterAPIError("No job running")

        response = client.delete("/api/v1/print/job")

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]
