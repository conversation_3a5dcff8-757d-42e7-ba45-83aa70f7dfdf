# Building Full-Stack Hardware Control Applications
## A Complete Guide to Modern Web-Based Industrial Control Systems

### Table of Contents
1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Technology Stack Deep Dive](#technology-stack-deep-dive)
4. [Project Structure and Organization](#project-structure-and-organization)
5. [Backend Development with FastAPI](#backend-development-with-fastapi)
6. [Frontend Development with Vue.js](#frontend-development-with-vuejs)
7. [Hardware Integration Patterns](#hardware-integration-patterns)
8. [Testing and Development Practices](#testing-and-development-practices)
9. [Deployment and Production Considerations](#deployment-and-production-considerations)
10. [Step-by-Step Tutorial](#step-by-step-tutorial)

---

## Introduction

This textbook teaches you how to build modern, web-based control systems for industrial hardware. Using the Recoater HMI (Human Machine Interface) project as a real-world example, you'll learn to create full-stack applications that provide intuitive web interfaces for complex hardware systems.

### What You'll Learn
- **Full-stack web development** for hardware control
- **Real-time communication** between web interfaces and hardware
- **Professional software architecture** patterns
- **Testing strategies** for hardware-integrated applications
- **Deployment practices** for industrial environments

### Prerequisites
- Basic programming knowledge (any language)
- Understanding of web concepts (HTTP, JSON)
- Familiarity with command-line interfaces

---

## Architecture Overview

Modern hardware control applications follow a **three-tier architecture**:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Hardware      │
│   (Vue.js)      │◄──►│   (FastAPI)     │◄──►│     API         │
│                 │    │                 │    │                 │
│ • User Interface│    │ • Business Logic│    │ • Physical      │
│ • Real-time UI  │    │ • API Gateway   │    │   Control       │
│ • State Mgmt    │    │ • WebSocket Hub │    │ • Sensors       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Why This Architecture?

**Separation of Concerns**: Each layer has a specific responsibility:
- **Frontend**: User experience and interface
- **Backend**: Business logic and hardware abstraction
- **Hardware**: Physical control and sensing

**Scalability**: Easy to add new features, interfaces, or hardware
**Maintainability**: Changes in one layer don't affect others
**Testability**: Each layer can be tested independently

---

## Technology Stack Deep Dive

### Backend: FastAPI (Python)

**FastAPI** is a modern Python web framework designed for building APIs.

#### Why FastAPI?
```python
# Automatic API documentation
@app.get("/status")
async def get_status() -> Dict[str, Any]:
    """Get system status - automatically documented!"""
    return {"status": "running"}
```

**Key Features:**
- **Type Hints**: Python's type hints allow you to explicitly declare the types of variables, function parameters, and return values. This makes your code more:

1. **Self-documenting**: It's immediately clear what types are expected
2. **Easier to debug**: Catch type-related errors before runtime
3. **IDE-friendly**: Better code completion and error detection

Example with type hints:
```python
def greet(name: str, age: int) -> str:
    """Returns a greeting message with the person's name and age."""
    return f"Hello {name}, you are {age} years old!"

# The IDE and tools will warn about type mismatches
greet(123, "twenty")  # Type error: expected str, got int; expected int, got str
- **Async support**: Handle many concurrent requests
- **OpenAPI integration**: Auto-generated API docs
- **Fast performance**: Comparable to Node.js and Go

#### Core Concepts

**1. Path Operations (Routes)**
```python
@app.get("/items/{item_id}")  # HTTP GET to /items/123
async def read_item(item_id: int):
    return {"item_id": item_id}
```

**2. Request/Response Models**
```python
from pydantic import BaseModel

class Item(BaseModel):
    name: str
    price: float

@app.post("/items/")
async def create_item(item: Item):  # Automatic JSON validation
    return item
```

**3. Dependency Injection**
```python
def get_database():
    return Database()

@app.get("/users/")
async def read_users(db = Depends(get_database)):  # Automatic injection
    return db.get_users()
```

### Frontend: Vue.js 3

**Vue.js** is a progressive JavaScript framework for building user interfaces. It's designed to be incrementally adoptable, meaning you can start small and scale up as needed.

#### Why Vue.js?

**Core Concepts in Detail:**

1. **Reactivity System**
   - Vue automatically tracks JavaScript state changes and efficiently updates the DOM
   - Uses a virtual DOM to minimize direct DOM manipulation
   - Reactive data properties trigger view updates automatically

2. **Component System**
   - Build encapsulated, reusable components
   - Single File Components (.vue files) combine template, script, and styles
   - Props for parent-to-child communication
   - Custom events for child-to-parent communication

3. **Composition API**
   - More flexible code organization than Options API
   - Better TypeScript support
   - Logic reuse through composable functions

4. **Ecosystem**
   - Vue Router for navigation
   - Pinia for state management
   - Vue DevTools for debugging
   - Vite for fast development builds
- **Gentle learning curve**: Easy to start, powerful when needed
- **Reactive data**: UI automatically updates when data changes
- **Component-based**: Reusable, maintainable code
- **Excellent tooling**: Great developer experience

#### Core Concepts

**1. Reactive Data**
```javascript
import { ref, reactive } from 'vue'

// Reactive primitive
const count = ref(0)
count.value++  // UI automatically updates

// Reactive object
const state = reactive({
  name: 'John',
  age: 30
})
state.age++  // UI automatically updates
```

**2. Components**
```vue
<template>
  <div>
    <h1>{{ title }}</h1>
    <button @click="increment">Count: {{ count }}</button>
  </div>
</template>

<script>
export default {
  setup() {
    const title = ref('My App')
    const count = ref(0)

    const increment = () => count.value++

    return { title, count, increment }
  }
}
</script>
```

**3. State Management (Pinia)**
```javascript
// stores/counter.js
import { defineStore } from 'pinia'

export const useCounterStore = defineStore('counter', () => {
  const count = ref(0)
  const increment = () => count.value++

  return { count, increment }
})
```

### Communication Layer: WebSockets + HTTP

**HTTP**: Request-response for commands
**WebSockets**: Real-time bidirectional communication

```javascript
// HTTP for commands
await api.post('/axis/x/move', { distance: 10, speed: 5 })

// WebSocket for real-time updates
const ws = new WebSocket('ws://localhost:8000/ws')
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  updateUI(data)
}
```

---

## Project Structure and Organization

A well-organized project structure is crucial for maintainability:

```
project/
├── backend/                 # Python backend
│   ├── app/                # FastAPI application
│   │   ├── __init__.py
│   │   ├── main.py         # Application entry point
│   │   ├── dependencies.py # Dependency injection
│   │   └── api/            # API route modules
│   │       ├── __init__.py
│   │       ├── status.py   # Status endpoints
│   │       └── axis.py     # Axis control endpoints
│   ├── services/           # Business logic layer
│   │   ├── __init__.py
│   │   ├── recoater_client.py      # Hardware client
│   │   └── mock_recoater_client.py # Development mock
│   ├── tests/              # Test suite
│   ├── requirements.txt    # Python dependencies
│   └── .env               # Environment configuration
├── frontend/               # Vue.js frontend
│   ├── src/
│   │   ├── main.js        # Application entry point
│   │   ├── App.vue        # Root component
│   │   ├── views/         # Page components
│   │   ├── components/    # Reusable components
│   │   ├── stores/        # State management
│   │   └── services/      # API communication
│   ├── tests/             # Frontend tests
│   ├── package.json       # Node.js dependencies
│   └── vite.config.js     # Build configuration
├── docs/                  # Documentation
│   ├── README.md
│   ├── architecture.md
│   └── textbook.md        # This file!
└── install_deps.bat       # Setup script
```

### Why This Structure?

**Backend Organization:**
- `app/`: FastAPI-specific code
- `services/`: Business logic, hardware communication
- `tests/`: Comprehensive test coverage

**Frontend Organization:**
- `views/`: Full-page components (routes)
- `components/`: Reusable UI pieces
- `stores/`: Application state management
- `services/`: API communication logic

**Benefits:**
- **Clear separation**: Easy to find related code
- **Scalable**: Easy to add new features
- **Team-friendly**: Multiple developers can work without conflicts

---

## Backend Development with FastAPI

### Application Structure

The backend follows a **layered architecture**:

```python
# app/main.py - Application entry point
from fastapi import FastAPI
from app.api import status, axis
from app.dependencies import initialize_recoater_client

app = FastAPI(title="Recoater HMI Backend")
app.include_router(status.router, prefix="/api/v1")
app.include_router(axis.router, prefix="/api/v1")

@app.on_event("startup")
async def startup():
    initialize_recoater_client()
```

### Dependency Injection Pattern

**Problem**: How do we share the hardware client across all API endpoints without circular imports?

**Solution**: Centralized dependency injection

```python
# app/dependencies.py
from services.recoater_client import RecoaterClient

_recoater_client = None

def initialize_recoater_client():
    global _recoater_client
    _recoater_client = RecoaterClient("http://hardware:8080")

def get_recoater_client() -> RecoaterClient:
    if _recoater_client is None:
        raise HTTPException(503, "Client not initialized")
    return _recoater_client
```

**Usage in API endpoints:**
```python
# app/api/axis.py
from fastapi import Depends
from app.dependencies import get_recoater_client

@router.post("/axis/{axis}/move")
async def move_axis(
    axis: str,
    motion_data: MotionRequest,
    client = Depends(get_recoater_client)  # Automatic injection
):
    return client.move_axis(axis, motion_data.distance, motion_data.speed)
```

### Request/Response Models with Pydantic

## Understanding Pydantic

Pydantic is a data validation and settings management library that uses Python type annotations. It's particularly useful for:

1. **Data Validation**: Automatically validates data against specified types
2. **Settings Management**: Load and validate application settings
3. **Data Parsing**: Convert and validate data from different formats
4. **Documentation**: Automatically generate JSON Schema

### Key Features:

#### 1. Data Models
```python
from pydantic import BaseModel, Field, validator
from typing import List, Optional

class User(BaseModel):
    id: int
    name: str = Field(..., min_length=1, max_length=50)
    email: str
    age: Optional[int] = Field(None, ge=0, le=120)
    roles: List[str] = []
    
    @validator('email')
    def email_must_contain_at(cls, v):
        if '@' not in v:
            raise ValueError('must contain @')
        return v
```

#### 2. Type Conversion
```python
# Automatic type conversion
user = User(id='123', name='John', email='<EMAIL>', age='30')
print(user.age)  # 30 (converted from string to int)
```

#### 3. Error Handling
```python
try:
    User(id=1, name='', email='invalid', age=150)
except ValidationError as e:
    print(e)
    # Output:
    # 2 validation errors for User
    # name
    #   ensure this value has at least 1 characters (type=value_error.any_str.min_length)
    # email
    #   must contain @ (type=value_error)
```

#### 4. Integration with FastAPI
Pydantic models work seamlessly with FastAPI for request/response validation and automatic API documentation.

```python
from pydantic import BaseModel, Field

class MotionRequest(BaseModel):
    distance: float = Field(..., description="Distance in mm")
    speed: float = Field(gt=0, description="Speed in mm/s")
    mode: str = Field(default="relative", regex="^(relative|absolute)$")

# Usage
@app.post("/move")
async def move(motion: MotionRequest):
    # motion.distance is guaranteed to be a float
    # motion.speed is guaranteed to be > 0
    # motion.mode is guaranteed to be "relative" or "absolute"
    return {"status": "moving", "params": motion.dict()}
```

### Error Handling Strategy

**Layered error handling** provides clear error messages:

```python
# services/recoater_client.py
class RecoaterConnectionError(Exception):
    """Hardware connection failed"""
    pass

class RecoaterAPIError(Exception):
    """Hardware returned error"""
    pass

# app/api/axis.py
@router.post("/move")
async def move_axis(motion: MotionRequest, client = Depends(get_recoater_client)):
    try:
        result = client.move_axis(motion.distance, motion.speed)
        return {"success": True, "result": result}
    except RecoaterConnectionError as e:
        raise HTTPException(503, f"Hardware connection failed: {e}")
    except RecoaterAPIError as e:
        raise HTTPException(502, f"Hardware error: {e}")
    except Exception as e:
        raise HTTPException(500, f"Internal error: {e}")
```

### WebSocket Real-time Communication

**WebSockets** enable real-time updates without polling:

```python
# Connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            await connection.send_json(message)

manager = ConnectionManager()

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            await websocket.receive_text()  # Keep alive
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Background task for status updates
async def status_polling_task():
    while True:
        status = get_hardware_status()
        await manager.broadcast({"type": "status", "data": status})
        await asyncio.sleep(1.0)
```

### Hardware Abstraction Layer

**Abstract hardware complexity** behind a clean interface:

```python
# services/recoater_client.py
class RecoaterClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()

    def _make_request(self, method: str, endpoint: str, **kwargs):
        """Centralized request handling with error management"""
        url = f"{self.base_url}/{endpoint}"
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.ConnectionError as e:
            raise RecoaterConnectionError(f"Connection failed: {e}")
        except requests.HTTPError as e:
            raise RecoaterAPIError(f"API error: {e}")

    def get_state(self) -> dict:
        """Get current hardware state"""
        return self._make_request("GET", "/state")

    def move_axis(self, axis: str, distance: float, speed: float) -> dict:
        """Move hardware axis"""
        payload = {"distance": distance, "speed": speed}
        return self._make_request("POST", f"/axis/{axis}/move", json=payload)
```

### Development vs Production

**Mock client** for development without hardware:

```python
# services/mock_recoater_client.py
class MockRecoaterClient:
    """Simulates hardware for development"""

    def __init__(self, base_url: str):
        self.base_url = base_url
        self._state = {"status": "idle", "position": 0.0}

    def get_state(self) -> dict:
        return self._state.copy()

    def move_axis(self, axis: str, distance: float, speed: float) -> dict:
        # Simulate movement
        self._state["position"] += distance
        return {"success": True, "new_position": self._state["position"]}

# app/dependencies.py
def initialize_recoater_client():
    global _recoater_client
    if os.getenv("DEVELOPMENT_MODE") == "true":
        _recoater_client = MockRecoaterClient(base_url)
    else:
        _recoater_client = RecoaterClient(base_url)
```

---

## Frontend Development with Vue.js

### Component Architecture

Vue.js applications are built with **reusable components**:

```vue
<!-- components/AxisControl.vue -->
<template>
  <div class="axis-control">
    <h3>{{ axisName }} Axis</h3>
    <div class="status">
      Position: {{ position.toFixed(2) }}mm
      Status: {{ isMoving ? 'Moving' : 'Stopped' }}
    </div>
    <div class="controls">
      <button @click="move(-distance)" :disabled="isMoving">←</button>
      <input v-model.number="distance" type="number" step="0.1" />
      <button @click="move(distance)" :disabled="isMoving">→</button>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useStatusStore } from '@/stores/status'
import apiService from '@/services/api'

export default {
  name: 'AxisControl',
  props: {
    axisName: { type: String, required: true },
    axis: { type: String, required: true }  // 'x' or 'z'
  },
  setup(props) {
    const statusStore = useStatusStore()
    const distance = ref(10.0)

    // Computed properties automatically update when store changes
    const position = computed(() =>
      statusStore.axisData?.[props.axis]?.position || 0
    )
    const isMoving = computed(() =>
      statusStore.axisData?.[props.axis]?.running || false
    )

    const move = async (dist) => {
      try {
        await apiService.moveAxis(props.axis, {
          distance: dist,
          speed: 5.0,
          mode: 'relative'
        })
      } catch (error) {
        console.error('Move failed:', error)
      }
    }

    return { distance, position, isMoving, move }
  }
}
</script>
```

### State Management with Pinia

**Centralized state** keeps data consistent across components:

```javascript
// stores/status.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useStatusStore = defineStore('status', () => {
  // State
  const isConnected = ref(false)
  const statusData = ref(null)
  const axisData = ref(null)
  const error = ref(null)

  // Getters (computed properties)
  const systemStatus = computed(() =>
    statusData.value?.state || 'unknown'
  )

  const axisPosition = computed(() => (axis) =>
    axisData.value?.[axis]?.position || 0
  )

  // Actions (methods)
  const updateStatus = (newStatus) => {
    statusData.value = newStatus
    isConnected.value = true
    error.value = null
  }

  const updateAxisData = (newAxisData) => {
    axisData.value = newAxisData
  }

  const setError = (errorMessage) => {
    error.value = errorMessage
    isConnected.value = false
  }

  return {
    // State
    isConnected,
    statusData,
    axisData,
    error,
    // Getters
    systemStatus,
    axisPosition,
    // Actions
    updateStatus,
    updateAxisData,
    setError
  }
})
```

### API Service Layer

**Centralized API communication** with error handling:

```javascript
// services/api.js
import axios from 'axios'

const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export default {
  // Status endpoints
  getStatus() {
    return apiClient.get('/status')
  },

  // Axis control endpoints
  moveAxis(axis, motionData) {
    return apiClient.post(`/axis/${axis}/motion`, motionData)
  },

  homeAxis(axis, homingData) {
    return apiClient.post(`/axis/${axis}/home`, homingData)
  },

  getAxisStatus(axis) {
    return apiClient.get(`/axis/${axis}`)
  }
}
```

### Real-time Updates with WebSockets

**WebSocket integration** for live data:

```javascript
// services/websocket.js
import { useStatusStore } from '@/stores/status'

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 1000
  }

  connect() {
    try {
      this.ws = new WebSocket('ws://localhost:8000/ws')

      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.reconnectAttempts = 0
      }

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      }

      this.ws.onclose = () => {
        console.log('WebSocket disconnected')
        this.reconnect()
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error)
      }
    } catch (error) {
      console.error('WebSocket connection failed:', error)
      this.reconnect()
    }
  }

  handleMessage(data) {
    const statusStore = useStatusStore()

    switch (data.type) {
      case 'status_update':
        statusStore.updateStatus(data.data)
        if (data.axis_data) {
          statusStore.updateAxisData(data.axis_data)
        }
        break
      case 'connection_error':
        statusStore.setError(data.error)
        break
      default:
        console.log('Unknown message type:', data.type)
    }
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      setTimeout(() => this.connect(), this.reconnectInterval)
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}

export default new WebSocketService()
```

### Vue Router for Navigation

**Single Page Application** routing:

```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import StatusView from '@/views/StatusView.vue'
import AxisView from '@/views/AxisView.vue'
import RecoaterView from '@/views/RecoaterView.vue'

const routes = [
  { path: '/', redirect: '/status' },
  { path: '/status', component: StatusView },
  { path: '/axis', component: AxisView },
  { path: '/recoater', component: RecoaterView }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

---

## Hardware Integration Patterns

### API Design Principles

**RESTful API design** for hardware control:

```
GET    /api/v1/status           # Get system status
GET    /api/v1/axis/x           # Get X-axis status
POST   /api/v1/axis/x/motion    # Move X-axis
DELETE /api/v1/axis/x/motion    # Stop X-axis motion
PUT    /api/v1/config           # Update configuration
```

**Key principles:**
- **Nouns for resources**: `/axis/x` not `/moveAxis`
- **HTTP verbs for actions**: `POST` to create, `PUT` to update
- **Consistent naming**: Use same patterns throughout
- **Hierarchical structure**: `/axis/x/motion` shows relationship

### Error Handling Strategy

**Layered error handling** provides clear feedback:

```python
# Hardware layer errors
class HardwareError(Exception):
    pass

class ConnectionError(HardwareError):
    pass

class CommandError(HardwareError):
    pass

# API layer error mapping
@app.exception_handler(ConnectionError)
async def connection_error_handler(request, exc):
    return JSONResponse(
        status_code=503,
        content={"error": "Hardware connection failed", "detail": str(exc)}
    )

@app.exception_handler(CommandError)
async def command_error_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": "Invalid command", "detail": str(exc)}
    )
```

### Development vs Production

**Environment-specific behavior**:

```python
# Development: Mock hardware
if os.getenv("DEVELOPMENT_MODE") == "true":
    hardware_client = MockHardwareClient()
else:
    hardware_client = RealHardwareClient()

# Mock client simulates real behavior
class MockHardwareClient:
    def __init__(self):
        self.position = 0.0
        self.moving = False

    def move_axis(self, distance, speed):
        self.moving = True
        # Simulate movement time
        time.sleep(abs(distance) / speed)
        self.position += distance
        self.moving = False
        return {"success": True, "position": self.position}
```

### Real-time Communication Patterns

**WebSocket message types**:

```javascript
// Status updates
{
  "type": "status_update",
  "data": {
    "timestamp": 1640995200,
    "connected": true,
    "state": {"status": "running"},
    "axis": {"x": {"position": 10.5, "moving": false}}
  }
}

// Error notifications
{
  "type": "error",
  "data": {
    "timestamp": 1640995200,
    "severity": "warning",
    "message": "Axis limit reached",
    "component": "x_axis"
  }
}

// Command acknowledgments
{
  "type": "command_ack",
  "data": {
    "command_id": "move_x_123",
    "status": "accepted",
    "estimated_duration": 5.0
  }
}
```

---

## API Documentation with FastAPI and OpenAPI

FastAPI automatically generates interactive API documentation using the OpenAPI standard. Here's how it works:

### Automatic Documentation Generation

1. **Built-in Documentation UIs**
   - **Swagger UI**: Available at `/docs`
   - **ReDoc**: Available at `/redoc`

2. **How It Works**
   - Uses Python type hints to determine parameter types
   - Reads docstrings for descriptions
   - Validates requests and responses
   - Generates JSON Schema for all models

3. **Customizing Documentation**
```python
from fastapi import FastAPI, status
from pydantic import BaseModel

app = FastAPI(
    title="Recoater API",
    description="API for controlling the Recoater system",
    version="1.0.0",
)

class Item(BaseModel):
    name: str
    description: str | None = None
    price: float
    tax: float | None = None

@app.post(
    "/items/",
    response_model=Item,
    status_code=status.HTTP_201_CREATED,
    summary="Create an item",
    description="Creates a new item in the system with the given details.",
    response_description="The created item",
    tags=["items"],
)
async def create_item(item: Item):
    """
    Create an item with all the information:

    - **name**: each item must have a name
    - **description**: a long description (optional)
    - **price**: required, must be positive
    - **tax**: if the item doesn't have tax, we consider 0.0
    """
    return item
```

4. **Documenting Responses**
```python
from fastapi.responses import JSONResponse

@app.get("/items/{item_id}",
    responses={
        200: {
            "description": "Found the item",
            "content": {
                "application/json": {
                    "example": {"id": 1, "name": "Example Item"}
                }
            },
        },
        404: {
            "description": "Item not found",
            "content": {
                "application/json": {
                    "example": {"detail": "Item not found"}
                }
            },
        },
    },
)
async def read_item(item_id: int):
    if item_id == 1:
        return {"id": 1, "name": "Example Item"}
    return JSONResponse(
        status_code=404,
        content={"detail": "Item not found"},
    )
```

5. **Adding Examples**
```python
class Item(BaseModel):
    name: str = Field(..., example="Widget")
    description: str | None = Field(
        None, 
        example="A high-quality widget for all your needs"
    )
    price: float = Field(..., gt=0, example=9.99)
    tax: float | None = Field(None, example=0.99)

    class Config:
        schema_extra = {
            "example": {
                "name": "Widget",
                "description": "A high-quality widget",
                "price": 9.99,
                "tax": 0.99,
            }
        }
```

## State Management with Pinia

### What is Pinia?
Pinia is the official state management library for Vue.js applications. It serves as a centralized store for all the components in an application, with rules ensuring that the state can only be mutated in a predictable fashion.

### Centralized State Management
In a typical Vue application, components manage their own state. However, as applications grow, sharing state between components becomes complex. Pinia solves this by providing a centralized store with:

1. **Single Source of Truth**: One place where all shared state lives
2. **Predictable State Mutations**: State can only be changed through defined actions
3. **Reactivity**: Components automatically update when state changes
4. **DevTools Integration**: Track state changes and time-travel debugging

### Basic Pinia Store Example
```javascript
// stores/counter.js
import { defineStore } from 'pinia'

export const useCounterStore = defineStore('counter', {
  state: () => ({
    count: 0,
    name: 'Counter'
  }),
  getters: {
    doubleCount: (state) => state.count * 2,
  },
  actions: {
    increment() {
      this.count++
    },
    reset() {
      this.count = 0
    }
  }
})

// Component usage
import { useCounterStore } from '@/stores/counter'

export default {
  setup() {
    const counter = useCounterStore()
    return { counter }
  }
}
```

## Frontend Project Structure

### Setting Up the Frontend
A typical Vue 3 + Pinia project structure looks like this:

```
frontend/
├── public/                # Static files
├── src/
│   ├── assets/            # Images, fonts, etc.
│   ├── components/        # Reusable UI components
│   │   ├── ui/            # Basic UI components (buttons, inputs, etc.)
│   │   └── layout/        # Layout components (header, footer, etc.)
│   ├── composables/       # Composable functions
│   ├── router/            # Vue Router configuration
│   │   └── index.js
│   ├── stores/            # Pinia stores
│   │   ├── index.js       # Main store initialization
│   │   └── modules/       # Individual store modules
│   ├── styles/            # Global styles
│   ├── utils/             # Utility functions
│   ├── views/             # Page components
│   ├── App.vue            # Root component
│   └── main.js            # Application entry point
├── .env                   # Environment variables
└── vite.config.js         # Vite configuration
```

### Key Files Explained

1. **main.js** - Application entry point:
```javascript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
```

2. **router/index.js** - Route configuration:
```javascript
import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('../views/AboutView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router
```

## Understanding Circular Imports

### What is a Circular Import?
A circular import occurs when two or more modules depend on each other, either directly or indirectly. This can cause issues because:

1. Modules may be partially initialized when imported
2. Can lead to undefined values
3. Makes code harder to understand and maintain

### Example of Circular Import
```javascript
// fileA.js
import { b } from './fileB.js'
export const a = 'A' + b

// fileB.js
import { a } from './fileA.js'  // Circular import!
export const b = 'B' + a
```

### Solutions
1. **Restructure Code**: Move shared code to a third module
2. **Dynamic Imports**: Import inside functions when needed
3. **Dependency Injection**: Pass dependencies as parameters

## WebSockets in Modern Web Applications

### What are WebSockets?
WebSockets provide a persistent, full-duplex communication channel over a single TCP connection, enabling real-time data flow between client and server.

### Key Features
- **Bidirectional Communication**: Both server and client can send messages
- **Low Latency**: No HTTP overhead after initial handshake
- **Persistent Connection**: Remains open until explicitly closed
- **Real-time Updates**: Perfect for live data applications

### WebSocket Implementation Example

**Frontend (Vue.js)**
```javascript
// src/utils/websocket.js
export class WebSocketService {
  constructor(url) {
    this.ws = null
    this.url = url
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.subscribers = new Set()
  }

  connect() {
    this.ws = new WebSocket(this.url)

    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
    }

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.notifySubscribers(data)
    }

    this.ws.onclose = () => {
      console.log('WebSocket disconnected')
      this.handleReconnect()
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error)
      this.ws.close()
    }
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000)
      console.log(`Reconnecting in ${delay}ms...`)
      
      setTimeout(() => {
        this.reconnectAttempts++
        this.connect()
      }, delay)
    }
  }

  subscribe(callback) {
    this.subscribers.add(callback)
    return () => this.unsubscribe(callback)
  }

  unsubscribe(callback) {
    this.subscribers.delete(callback)
  }

  notifySubscribers(data) {
    this.subscribers.forEach(callback => callback(data))
  }

  send(message) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.error('WebSocket is not connected')
    }
  }
}

// Usage in a Vue component
import { ref, onMounted, onUnmounted } from 'vue'
import { WebSocketService } from '@/utils/websocket'

export default {
  setup() {
    const wsService = new WebSocketService('ws://localhost:8000/ws')
    const messages = ref([])

    const handleMessage = (data) => {
      messages.value.push(data)
    }

    onMounted(() => {
      wsService.connect()
      wsService.subscribe(handleMessage)
    })

    onUnmounted(() => {
      wsService.unsubscribe(handleMessage)
    })

    return { messages }
  }
}
```

## Mock Client and Hardware Simulation

### How the Mock Client Works
In development, we often need to simulate hardware behavior without actual devices. Here's how the mock client integrates:

1. **Dependency Injection Pattern**
```python
# backend/app/core/dependencies.py
from app.config import settings

def get_hardware_client():
    if settings.USE_MOCK_CLIENT:
        from app.hardware.mock_client import MockHardwareClient
        return MockHardwareClient()
    else:
        from app.hardware.real_client import RealHardwareClient
        return RealHardwareClient()

# In route handlers:
@app.get("/status")
async def get_status(hardware = Depends(get_hardware_client)):
    return await hardware.get_status()
```

2. **Environment-based Switching**
```env
# .env
USE_MOCK_CLIENT=true  # Set to false in production
```

3. **Mock Implementation**
```python
# backend/app/hardware/mock_client.py
import asyncio
from random import random
from typing import Dict, Any

class MockHardwareClient:
    def __init__(self):
        self.position = 0.0
        self.is_connected = False

    async def connect(self):
        await asyncio.sleep(0.1)  # Simulate connection delay
        self.is_connected = True
        return True

    async def move_to(self, position: float, speed: float):
        steps = abs(position - self.position) / speed
        for _ in range(int(steps)):
            self.position += speed * (1 if position > self.position else -1)
            await asyncio.sleep(0.1)  # Simulate movement
        self.position = position

    async def get_status(self) -> Dict[str, Any]:
        return {
            "position": self.position,
            "connected": self.is_connected,
            "temperature": 25.0 + random() * 5,  # Simulate temperature
            "voltage": 23.5 + random() * 0.5,    # Simulate voltage
        }
```

## Vue Router in Detail

### What is Vue Router?
Vue Router is the official router for Vue.js applications, enabling navigation between different views while maintaining application state.

### Key Features
1. **Nested Routes**: Organize routes in a nested layout
2. **Route Parameters**: Dynamic path segments
3. **Navigation Guards**: Control navigation with before/after hooks
4. **Lazy Loading**: Load components only when needed
5. **Transitions**: Animate between routes

### Advanced Router Configuration
```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: 'settings',
        component: () => import('@/views/Settings.vue'),
        meta: { requiresAdmin: true },
        children: [
          {
            path: 'profile',
            component: () => import('@/views/settings/Profile.vue')
          },
          {
            path: 'preferences',
            component: () => import('@/views/settings/Preferences.vue')
          }
        ]
      }
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/Login.vue'),
    meta: { guest: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation Guards
router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters['auth/isAuthenticated']
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)

  if (requiresAuth && !isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else if (to.meta.guest && isAuthenticated) {
    next({ name: 'dashboard' })
  } else if (requiresAdmin && !store.getters['auth/isAdmin']) {
    next({ name: 'unauthorized' })
  } else {
    next()
  }
})

export default router
```

### Using Router in Components
```vue
<template>
  <div>
    <nav>
      <router-link to="/">Home</router-link> |
      <router-link :to="{ name: 'about' }">About</router-link> |
      <router-link :to="{ name: 'user', params: { id: 123 }}">User</router-link>
    </nav>
    <router-view />
  </div>
</template>

<script>
export default {
  methods: {
    goToAbout() {
      this.$router.push('/about')
    },
    goToUserProfile(userId) {
      this.$router.push({ name: 'user', params: { id: userId }})
    },
    replaceRoute() {
      this.$router.replace('/login')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
```

## Testing and Development Practices

### Backend Testing with pytest

**Comprehensive test coverage**:

## Writing Effective Tests

### 1. Test File Structure
```python
# tests/test_axis_api.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch
from app.main import app

# Create a test client for making HTTP requests to the FastAPI app
client = TestClient(app)
```
**Key Points:**
- Imports necessary testing libraries
- Creates a test client that simulates HTTP requests to the FastAPI application

### 2. Test Class Definition
```python
class TestAxisAPI:
    """Test suite for the axis motion API endpoints."""
```
**Key Points:**
- Groups related test cases using a class
- Follows Python's unittest framework conventions

### 3. Testing Successful API Call
```python
    @patch('app.dependencies.get_recoater_client')
    def test_move_axis_success(self, mock_client):
        # Arrange
        # Configure the mock to return a success response
        mock_client.return_value.move_axis.return_value = {"success": True}

        # Act
        # Make a POST request to the axis motion endpoint
        response = client.post(
            "/api/v1/axis/x/motion", 
            json={
                "distance": 10.0,
                "speed": 5.0,
                "mode": "relative"
            }
        )

        # Assert
        # Verify the response status code and content
        assert response.status_code == 200
        assert response.json()["success"] == True
        
        # Verify the mock was called with expected arguments
        mock_client.return_value.move_axis.assert_called_once_with(
            "x", 10.0, 5.0, "relative"
        )
```
**Key Points:**
- Uses `@patch` to mock the hardware client
- Follows Arrange-Act-Assert pattern
- Verifies both the response and mock interactions

### 4. Testing Error Handling
```python
    @patch('app.dependencies.get_recoater_client')
    def test_move_axis_connection_error(self, mock_client):
        # Arrange
        # Configure the mock to raise a ConnectionError
        mock_client.return_value.move_axis.side_effect = ConnectionError("Hardware offline")
        
        # Act & Assert
        # Verify the API handles the error properly
        response = client.post(
            "/api/v1/axis/x/motion",
            json={"distance": 10.0, "speed": 5.0, "mode": "relative"}
        )
        
        # Assert
        assert response.status_code == 503  # Service Unavailable
        assert "Hardware offline" in response.json()["detail"]
```
**Key Points:**
- Tests error handling by simulating a hardware connection error
- Verifies proper HTTP status code is returned
- Checks that error details are included in the response

### 5. Testing Validation Errors
```python
    @patch('app.dependencies.get_recoater_client')
    def test_move_axis_validation_error(self, mock_client):
        # Act
        # Make a request with invalid data (missing required fields)
        response = client.post(
            "/api/v1/axis/x/motion",
            json={"speed": 5.0}  # Missing required 'distance' field
        )
        
        # Assert
        assert response.status_code == 422  # Unprocessable Entity
        assert "field required" in str(response.content)
```
**Key Points:**
- Tests API validation logic
- Verifies proper error response for invalid input
- Uses HTTP 422 for validation errors

### 6. Testing Authentication
```python
    @patch('app.dependencies.get_recoater_client')
    def test_move_axis_unauthorized(self, mock_client):
        # Act
        # Make a request without authentication
        response = client.post(
            "/api/v1/axis/x/motion",
            json={"distance": 10.0, "speed": 5.0, "mode": "relative"},
            headers={"Authorization": ""}  # Empty/invalid token
        )
        
        # Assert
        assert response.status_code == 401  # Unauthorized
        assert "Not authenticated" in response.json()["detail"]
```
**Key Points:**
- Tests authentication requirements
- Verifies proper handling of unauthorized access
- Uses HTTP 401 for authentication failures

### Frontend Testing with Vitest

**Component testing**:

```javascript
// tests/AxisControl.test.js
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import AxisControl from '@/components/AxisControl.vue'
import apiService from '@/services/api'

// Mock API service
vi.mock('@/services/api', () => ({
  default: {
    moveAxis: vi.fn()
  }
}))

describe('AxisControl', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('renders axis name and position', () => {
    const wrapper = mount(AxisControl, {
      props: { axisName: 'X Axis', axis: 'x' }
    })

    expect(wrapper.text()).toContain('X Axis')
    expect(wrapper.text()).toContain('Position:')
  })

  it('calls API when move button clicked', async () => {
    apiService.moveAxis.mockResolvedValue({ data: { success: true } })

    const wrapper = mount(AxisControl, {
      props: { axisName: 'X Axis', axis: 'x' }
    })

    // Set distance
    await wrapper.find('input[type="number"]').setValue('5.0')

    // Click move right button
    await wrapper.findAll('button')[1].trigger('click')

    expect(apiService.moveAxis).toHaveBeenCalledWith('x', {
      distance: 5.0,
      speed: 5.0,
      mode: 'relative'
    })
  })
})
```

### Integration Testing

**End-to-end testing** with real API calls:

```python
# tests/test_integration.py
import pytest
import asyncio
from fastapi.testclient import TestClient
from app.main import app

class TestIntegration:
    def test_full_axis_control_flow(self):
        client = TestClient(app)

        # 1. Check initial status
        response = client.get("/api/v1/status")
        assert response.status_code == 200

        # 2. Move axis
        response = client.post("/api/v1/axis/x/motion", json={
            "distance": 10.0,
            "speed": 5.0,
            "mode": "relative"
        })
        assert response.status_code == 200

        # 3. Check axis status
        response = client.get("/api/v1/axis/x")
        assert response.status_code == 200

        # 4. Stop motion
        response = client.delete("/api/v1/axis/x/motion")
        assert response.status_code == 200
```

### Advanced Testing Patterns

#### Testing FastAPI Dependency Injection

**Challenge**: How do you test endpoints that use dependency injection?

**Wrong Approach** (doesn't work):
```python
@patch('app.dependencies.get_recoater_client')  # This won't work!
def test_endpoint(mock_get_client):
    # FastAPI's DI system bypasses this mock
    pass
```

**Correct Approach** (fixture-based):
```python
# tests/conftest.py
import pytest
from unittest.mock import Mock
import app.dependencies as deps

@pytest.fixture(autouse=True)
def setup_test_dependencies():
    """Automatically setup mock dependencies for all tests."""
    # Create mock client
    mock_client = Mock()
    mock_client.get_state.return_value = {"status": "idle"}

    # Replace the actual instance
    original_client = deps._recoater_client
    deps._recoater_client = mock_client

    yield mock_client

    # Restore after test
    deps._recoater_client = original_client

# tests/test_api.py
def test_get_status(client, mock_recoater_client):
    """Test with automatic dependency mocking."""
    # Customize mock behavior for this test
    mock_recoater_client.get_state.return_value = {"status": "running"}

    response = client.get("/api/v1/status")
    assert response.status_code == 200
    assert response.json()["status"] == "running"
```

#### Different Testing Strategies by Layer

**1. API Layer Testing** (test endpoints):
```python
def test_api_endpoint(client, mock_recoater_client):
    """Test API endpoints with mocked dependencies."""
    mock_recoater_client.get_state.return_value = {"status": "ready"}

    response = client.get("/api/v1/status")
    assert response.status_code == 200
```

**2. Service Layer Testing** (test business logic):
```python
@patch('requests.Session.request')
def test_recoater_client(mock_request):
    """Test service layer with mocked HTTP requests."""
    mock_response = Mock()
    mock_response.json.return_value = {"status": "ready"}
    mock_request.return_value = mock_response

    client = RecoaterClient("http://test")
    result = client.get_state()
    assert result["status"] == "ready"
```

**3. Integration Testing** (test full flow):
```python
def test_full_workflow(client):
    """Test complete workflows end-to-end."""
    # Test actual API flow without mocking business logic
    response = client.get("/health")
    assert response.status_code == 200
```

#### Test Fixtures Best Practices

**Automatic Setup with autouse**:
```python
@pytest.fixture(autouse=True)
def setup_test_environment():
    """Runs before every test automatically."""
    # Setup code here
    yield
    # Cleanup code here
```

**Parameterized Tests**:
```python
@pytest.mark.parametrize("axis,expected", [
    ("x", {"position": 0.0}),
    ("z", {"position": 5.0}),
])
def test_axis_positions(client, mock_recoater_client, axis, expected):
    mock_recoater_client.get_axis_status.return_value = expected
    response = client.get(f"/api/v1/axis/{axis}")
    assert response.json()["status"] == expected
```

**Shared Fixtures**:
```python
@pytest.fixture
def sample_motion_data():
    """Reusable test data."""
    return {
        "distance": 10.0,
        "speed": 5.0,
        "mode": "relative"
    }

def test_motion(client, sample_motion_data):
    response = client.post("/api/v1/axis/x/motion", json=sample_motion_data)
    assert response.status_code == 200
```

#### Common Testing Pitfalls and Solutions

**Pitfall 1: Mocking at Wrong Level**
```python
# Wrong - mocks the function, not the instance
@patch('app.dependencies.get_recoater_client')

# Right - mocks the actual stored instance
deps._recoater_client = mock_client
```

**Pitfall 2: Shared State Between Tests**
```python
# Wrong - tests can affect each other
mock_client = Mock()  # Global mock

# Right - fresh mock for each test
@pytest.fixture
def fresh_mock():
    return Mock()
```

**Pitfall 3: Not Testing Error Cases**
```python
def test_error_handling(client, mock_recoater_client):
    """Always test error scenarios."""
    mock_recoater_client.get_state.side_effect = ConnectionError("Network down")

    response = client.get("/api/v1/status")
    assert response.status_code == 503
    assert "connection" in response.json()["detail"].lower()
```

#### Test Organization Patterns

**File Structure**:
```
tests/
├── conftest.py              # Shared fixtures
├── test_api_endpoints.py    # API layer tests
├── test_services.py         # Business logic tests
├── test_integration.py      # End-to-end tests
└── fixtures/
    ├── sample_data.py       # Test data
    └── mock_responses.py    # Mock response data
```

**Test Naming Convention**:
```python
def test_[what]_[when]_[expected]():
    """Clear test names describe behavior."""
    pass

# Examples:
def test_get_status_when_connected_returns_status_data():
def test_move_axis_when_invalid_distance_returns_validation_error():
def test_health_check_when_hardware_offline_returns_unhealthy():
```

---

## Step-by-Step Tutorial

### Prerequisites Setup

**1. Install Required Software**
```bash
# Python 3.8+ and Node.js 16+
python --version  # Should be 3.8+
node --version    # Should be 16+
npm --version
```

**2. Create Project Structure**
```bash
mkdir hardware-control-app
cd hardware-control-app
mkdir backend frontend docs
```

### Backend Development

**Step 1: Initialize Python Project**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install fastapi uvicorn requests python-dotenv pydantic
```

**Step 2: Create Basic FastAPI App**
```python
# backend/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Hardware Control API")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hardware Control API"}

@app.get("/status")
async def get_status():
    return {"status": "running", "timestamp": "2024-01-01T00:00:00Z"}
```

**Step 3: Add Hardware Client**
```python
# backend/hardware_client.py
import requests
from typing import Dict, Any

class HardwareClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()

    def get_status(self) -> Dict[str, Any]:
        # In real implementation, this would call actual hardware
        return {"connected": True, "status": "idle"}

    def move_axis(self, axis: str, distance: float, speed: float) -> Dict[str, Any]:
        # In real implementation, this would send command to hardware
        return {"success": True, "axis": axis, "distance": distance}

# Mock for development
class MockHardwareClient(HardwareClient):
    def __init__(self):
        self.position = {"x": 0.0, "y": 0.0, "z": 0.0}

    def move_axis(self, axis: str, distance: float, speed: float) -> Dict[str, Any]:
        self.position[axis] += distance
        return {
            "success": True,
            "axis": axis,
            "new_position": self.position[axis]
        }
```

**Step 4: Add API Endpoints**
```python
# backend/main.py (updated)
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from hardware_client import MockHardwareClient

app = FastAPI(title="Hardware Control API")
hardware = MockHardwareClient()

class MoveRequest(BaseModel):
    distance: float
    speed: float

@app.post("/axis/{axis}/move")
async def move_axis(axis: str, request: MoveRequest):
    try:
        result = hardware.move_axis(axis, request.distance, request.speed)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

**Step 5: Run Backend**
```bash
uvicorn main:app --reload --port 8000
# Visit http://localhost:8000/docs for API documentation
```

### Frontend Development

**Step 1: Initialize Vue.js Project**
```bash
cd ../frontend
npm create vue@latest .
# Choose: TypeScript: No, Router: Yes, Pinia: Yes, Testing: Vitest
npm install
npm install axios
```

**Step 2: Create API Service**
```javascript
// frontend/src/services/api.js
import axios from 'axios'

const apiClient = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 5000
})

export default {
  getStatus() {
    return apiClient.get('/status')
  },

  moveAxis(axis, distance, speed) {
    return apiClient.post(`/axis/${axis}/move`, { distance, speed })
  }
}
```

**Step 3: Create Axis Control Component**
```vue
<!-- frontend/src/components/AxisControl.vue -->
<template>
  <div class="axis-control">
    <h3>{{ axisName }}</h3>
    <div class="controls">
      <input v-model.number="distance" type="number" step="0.1" />
      <button @click="move(-distance)">←</button>
      <button @click="move(distance)">→</button>
    </div>
    <div v-if="status" class="status">{{ status }}</div>
  </div>
</template>

<script>
import { ref } from 'vue'
import api from '@/services/api'

export default {
  props: {
    axisName: String,
    axis: String
  },
  setup(props) {
    const distance = ref(10)
    const status = ref('')

    const move = async (dist) => {
      try {
        status.value = 'Moving...'
        const response = await api.moveAxis(props.axis, dist, 5.0)
        status.value = `Moved to ${response.data.new_position}`
      } catch (error) {
        status.value = `Error: ${error.message}`
      }
    }

    return { distance, status, move }
  }
}
</script>

<style scoped>
.axis-control {
  border: 1px solid #ccc;
  padding: 1rem;
  margin: 1rem;
  border-radius: 4px;
}
.controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}
</style>
```

**Step 4: Create Main View**
```vue
<!-- frontend/src/views/ControlView.vue -->
<template>
  <div class="control-view">
    <h1>Hardware Control</h1>
    <div class="status" v-if="systemStatus">
      System Status: {{ systemStatus.status }}
    </div>
    <div class="axes">
      <AxisControl axis-name="X Axis" axis="x" />
      <AxisControl axis-name="Y Axis" axis="y" />
      <AxisControl axis-name="Z Axis" axis="z" />
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import AxisControl from '@/components/AxisControl.vue'
import api from '@/services/api'

export default {
  components: { AxisControl },
  setup() {
    const systemStatus = ref(null)

    const loadStatus = async () => {
      try {
        const response = await api.getStatus()
        systemStatus.value = response.data
      } catch (error) {
        console.error('Failed to load status:', error)
      }
    }

    onMounted(loadStatus)

    return { systemStatus }
  }
}
</script>
```

**Step 5: Update Router**
```javascript
// frontend/src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import ControlView from '@/views/ControlView.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: ControlView }
  ]
})

export default router
```

**Step 6: Run Frontend**
```bash
npm run dev
# Visit http://localhost:5173
```

### Testing Your Application

**Step 1: Test Backend API**
```bash
# Test status endpoint
curl http://localhost:8000/status

# Test move endpoint
curl -X POST http://localhost:8000/axis/x/move \
  -H "Content-Type: application/json" \
  -d '{"distance": 10.0, "speed": 5.0}'
```

**Step 2: Test Frontend**
- Open browser to http://localhost:5173
- Try moving each axis using the controls
- Check browser console for any errors

### Deployment Considerations

**Production Setup:**
1. **Environment Variables**: Use `.env` files for configuration
2. **HTTPS**: Enable SSL/TLS for production
3. **Process Management**: Use systemd or Docker for service management
4. **Monitoring**: Add logging and health checks
5. **Security**: Implement authentication and authorization

**Docker Deployment:**
```dockerfile
# Dockerfile for backend
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# Dockerfile for frontend
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html
```

---

## Conclusion

You now have the knowledge to build professional hardware control applications using modern web technologies. The patterns and practices shown here scale from simple prototypes to complex industrial systems.

**Key Takeaways:**
- **Layered architecture** separates concerns and improves maintainability
- **Type safety** with Pydantic and TypeScript prevents runtime errors
- **Real-time communication** with WebSockets provides responsive UIs
- **Comprehensive testing** ensures reliability in production
- **Mock implementations** enable development without hardware

**Next Steps:**
- Add authentication and user management
- Implement data logging and analytics
- Add advanced error recovery mechanisms
- Scale to multiple hardware devices
- Implement advanced UI features like charts and dashboards

Happy building! 🚀