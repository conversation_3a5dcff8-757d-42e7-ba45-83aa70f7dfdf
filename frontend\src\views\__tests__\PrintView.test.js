/**
 * Tests for PrintView component
 * =============================
 * 
 * Comprehensive test suite for the PrintView component including
 * layer parameters management and preview functionality.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import PrintView from '../PrintView.vue'
import { useStatusStore } from '../../stores/status'
import apiService from '../../services/api'

// Mock API service
vi.mock('../../services/api', () => ({
  default: {
    getLayerParameters: vi.fn(),
    setLayerParameters: vi.fn(),
    getLayerPreview: vi.fn()
  }
}))

// Mock status store
vi.mock('../../stores/status', () => ({
  useStatusStore: vi.fn()
}))

describe('PrintView', () => {
  let mockStatusStore

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    mockStatusStore = {
      isConnected: false,
      printData: null,
      connectWebSocket: vi.fn(),
      disconnectWebSocket: vi.fn()
    }

    useStatusStore.mockReturnValue(mockStatusStore)
  })

  describe('Component Rendering', () => {
    it('renders the print view with all sections', () => {
      const wrapper = mount(PrintView)

      expect(wrapper.find('.print-view').exists()).toBe(true)
      expect(wrapper.find('.view-title').text()).toBe('Print Control')
      expect(wrapper.find('.status-card').exists()).toBe(true)
      expect(wrapper.find('.control-card').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer Parameters')
      expect(wrapper.text()).toContain('Layer Preview')
    })

    it('shows disconnected status when not connected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      expect(wrapper.find('.status-disconnected').exists()).toBe(true)
      expect(wrapper.find('.status-text').text()).toBe('Disconnected')
    })

    it('shows connected status when connected', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('.status-connected').exists()).toBe(true)
      expect(wrapper.find('.status-text').text()).toBe('Connected')
    })

    it('shows disabled overlay when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const overlays = wrapper.findAll('.disabled-overlay')
      expect(overlays.length).toBeGreaterThan(0)
      expect(wrapper.text()).toContain('Connect to recoater to configure layer parameters')
      expect(wrapper.text()).toContain('Connect to recoater to view layer preview')
    })
  })

  describe('Layer Parameters', () => {
    it('renders all parameter input fields', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').exists()).toBe(true)
      expect(wrapper.find('#speed').exists()).toBe(true)
      expect(wrapper.find('#x-offset').exists()).toBe(true)
      expect(wrapper.find('.parameter-checkbox').exists()).toBe(true)
    })

    it('disables inputs when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').attributes('disabled')).toBeDefined()
      expect(wrapper.find('#speed').attributes('disabled')).toBeDefined()
      expect(wrapper.find('#x-offset').attributes('disabled')).toBeDefined()
      expect(wrapper.find('.parameter-checkbox').attributes('disabled')).toBeDefined()
    })

    it('enables inputs when connected', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('#filling-id').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('#speed').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('#x-offset').attributes('disabled')).toBeUndefined()
      expect(wrapper.find('.parameter-checkbox').attributes('disabled')).toBeUndefined()
    })

    it('calls loadParameters when Load Current button is clicked', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: {
          filling_id: 2,
          speed: 25.0,
          powder_saving: false,
          x_offset: 5.0
        }
      })

      const wrapper = mount(PrintView)
      const loadButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Current'))
      
      await loadButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getLayerParameters).toHaveBeenCalledOnce()
    })

    it('calls saveParameters when Save Parameters button is clicked', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)
      const saveButton = wrapper.findAll('button').find(btn => btn.text().includes('Save Parameters'))
      
      await saveButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.setLayerParameters).toHaveBeenCalledOnce()
    })

    it('validates parameters before saving', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      // Set invalid speed
      wrapper.vm.layerParams.speed = 0
      expect(wrapper.vm.isParametersValid).toBe(false)

      // Set valid speed
      wrapper.vm.layerParams.speed = 30
      expect(wrapper.vm.isParametersValid).toBe(true)
    })

    it('disables save button when parameters are invalid', async () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      // Set invalid parameters
      wrapper.vm.layerParams.speed = 0
      await wrapper.vm.$nextTick()

      const saveButton = wrapper.findAll('button').find(btn => btn.text().includes('Save Parameters'))
      expect(saveButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Layer Preview', () => {
    it('shows preview placeholder when no image is loaded', () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      expect(wrapper.find('.preview-placeholder').exists()).toBe(true)
      expect(wrapper.find('.preview-icon').exists()).toBe(true)
      expect(wrapper.text()).toContain('No preview available')
    })

    it('shows loading state when preview is loading', async () => {
      mockStatusStore.isConnected = true
      const wrapper = mount(PrintView)

      wrapper.vm.previewLoading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.preview-loading').exists()).toBe(true)
      expect(wrapper.find('.loading-spinner').exists()).toBe(true)
      expect(wrapper.text()).toContain('Loading preview...')
    })

    it('calls loadPreview when Load Preview button is clicked', async () => {
      mockStatusStore.isConnected = true
      
      // Mock blob response
      const mockBlob = new Blob(['mock image data'], { type: 'image/png' })
      apiService.getLayerPreview.mockResolvedValue({ data: mockBlob })

      const wrapper = mount(PrintView)
      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      
      await previewButton.trigger('click')
      await wrapper.vm.$nextTick()

      expect(apiService.getLayerPreview).toHaveBeenCalledOnce()
    })

    it('disables preview button when disconnected', () => {
      mockStatusStore.isConnected = false
      const wrapper = mount(PrintView)

      const previewButton = wrapper.findAll('button').find(btn => btn.text().includes('Load Preview'))
      expect(previewButton.attributes('disabled')).toBeDefined()
    })
  })

  describe('Error Handling', () => {
    it('shows error message when parameter loading fails', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockRejectedValue(new Error('Connection failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.loadParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to load layer parameters')
    })

    it('shows error message when parameter saving fails', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockRejectedValue(new Error('Save failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.saveParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to save layer parameters')
    })

    it('shows error message when preview loading fails', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerPreview.mockRejectedValue(new Error('Preview failed'))

      const wrapper = mount(PrintView)
      await wrapper.vm.loadPreview()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('Failed to load layer preview')
    })
  })

  describe('Success Messages', () => {
    it('shows success message when parameters are loaded successfully', async () => {
      mockStatusStore.isConnected = true
      apiService.getLayerParameters.mockResolvedValue({
        data: { filling_id: 1, speed: 30.0, powder_saving: true, x_offset: 0.0 }
      })

      const wrapper = mount(PrintView)
      await wrapper.vm.loadParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-success').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer parameters loaded successfully')
    })

    it('shows success message when parameters are saved successfully', async () => {
      mockStatusStore.isConnected = true
      apiService.setLayerParameters.mockResolvedValue({ data: { success: true } })

      const wrapper = mount(PrintView)
      await wrapper.vm.saveParameters()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.message-success').exists()).toBe(true)
      expect(wrapper.text()).toContain('Layer parameters saved successfully')
    })
  })

  describe('WebSocket Integration', () => {
    it('connects to WebSocket on mount', () => {
      mount(PrintView)
      expect(mockStatusStore.connectWebSocket).toHaveBeenCalledOnce()
    })
  })

  describe('Component Lifecycle', () => {
    it('cleans up object URLs on unmount', () => {
      const wrapper = mount(PrintView)
      
      // Mock URL.revokeObjectURL
      const mockRevoke = vi.spyOn(URL, 'revokeObjectURL').mockImplementation(() => {})
      
      // Set a preview URL
      wrapper.vm.previewImageUrl = 'blob:mock-url'
      
      wrapper.unmount()
      
      expect(mockRevoke).toHaveBeenCalledWith('blob:mock-url')
      mockRevoke.mockRestore()
    })
  })
})
