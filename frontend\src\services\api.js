/**
 * API Service
 * ===========
 * 
 * Centralized service for making HTTP requests to the backend API.
 * All axios calls should go through this service to maintain consistency
 * and enable easy mocking for tests.
 */

import axios from 'axios'

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for logging and error handling
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

/**
 * API service object containing all API methods
 */
const apiService = {
  /**
   * Get the current status of the recoater system
   * @returns {Promise} Axios response promise
   */
  getStatus() {
    return apiClient.get('/status')
  },

  /**
   * Perform a health check
   * @returns {Promise} Axios response promise
   */
  getHealth() {
    return apiClient.get('/status/health')
  },

  /**
   * Get recoater configuration
   * @returns {Promise} Axios response promise
   */
  getConfig() {
    return apiClient.get('/config')
  },

  /**
   * Set recoater configuration
   * @param {Object} config - Configuration object
   * @returns {Promise} Axios response promise
   */
  setConfig(config) {
    return apiClient.put('/config', config)
  },

  /**
   * Get drums information
   * @returns {Promise} Axios response promise
   */
  getDrums() {
    return apiClient.get('/drums')
  },

  /**
   * Get specific drum information
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrum(drumId) {
    return apiClient.get(`/drums/${drumId}`)
  },

  // Axis Control API Methods

  /**
   * Get axis status
   * @param {string} axis - Axis identifier ('x', 'z', or 'gripper')
   * @returns {Promise} Axios response promise
   */
  getAxisStatus(axis) {
    return apiClient.get(`/axis/${axis}`)
  },

  /**
   * Move an axis
   * @param {string} axis - Axis identifier ('x' or 'z')
   * @param {Object} motionData - Motion parameters {distance, speed, mode}
   * @returns {Promise} Axios response promise
   */
  moveAxis(axis, motionData) {
    return apiClient.post(`/axis/${axis}/motion`, motionData)
  },

  /**
   * Home an axis
   * @param {string} axis - Axis identifier ('x' or 'z')
   * @param {Object} homingData - Homing parameters {speed}
   * @returns {Promise} Axios response promise
   */
  homeAxis(axis, homingData) {
    return apiClient.post(`/axis/${axis}/home`, homingData)
  },

  /**
   * Get axis motion status
   * @param {string} axis - Axis identifier ('x' or 'z')
   * @returns {Promise} Axios response promise
   */
  getAxisMotion(axis) {
    return apiClient.get(`/axis/${axis}/motion`)
  },

  /**
   * Cancel axis motion
   * @param {string} axis - Axis identifier ('x' or 'z')
   * @returns {Promise} Axios response promise
   */
  cancelAxisMotion(axis) {
    return apiClient.delete(`/axis/${axis}/motion`)
  },

  /**
   * Set gripper state
   * @param {Object} gripperData - Gripper state {enabled}
   * @returns {Promise} Axios response promise
   */
  setGripperState(gripperData) {
    return apiClient.put('/axis/gripper/state', gripperData)
  },

  /**
   * Get gripper state
   * @returns {Promise} Axios response promise
   */
  getGripperState() {
    return apiClient.get('/axis/gripper/state')
  },

  // Drum Control API Methods

  /**
   * Get drum motion status
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrumMotion(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/motion`)
  },

  /**
   * Set drum motion
   * @param {number} drumId - Drum ID
   * @param {Object} motionData - Motion parameters (mode, speed, distance, turns)
   * @returns {Promise} Axios response promise
   */
  setDrumMotion(drumId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/motion`, motionData)
  },

  /**
   * Cancel drum motion
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  cancelDrumMotion(drumId) {
    return apiClient.delete(`/recoater/drums/${drumId}/motion`)
  },

  /**
   * Get drum ejection pressure
   * @param {number} drumId - Drum ID
   * @param {string} unit - Pressure unit ('pascal' or 'bar')
   * @returns {Promise} Axios response promise
   */
  getDrumEjection(drumId, unit = 'pascal') {
    return apiClient.get(`/recoater/drums/${drumId}/ejection`, { params: { unit } })
  },

  /**
   * Set drum ejection pressure
   * @param {number} drumId - Drum ID
   * @param {Object} ejectionData - Ejection parameters (target, unit)
   * @returns {Promise} Axios response promise
   */
  setDrumEjection(drumId, ejectionData) {
    return apiClient.put(`/recoater/drums/${drumId}/ejection`, ejectionData)
  },

  /**
   * Get drum suction pressure
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getDrumSuction(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/suction`)
  },

  /**
   * Set drum suction pressure
   * @param {number} drumId - Drum ID
   * @param {Object} suctionData - Suction parameters (target)
   * @returns {Promise} Axios response promise
   */
  setDrumSuction(drumId, suctionData) {
    return apiClient.put(`/recoater/drums/${drumId}/suction`, suctionData)
  },

  // Blade Control API Methods

  /**
   * Get blade screws info
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewsInfo(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws`)
  },

  /**
   * Get blade screws motion status
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewsMotion(drumId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/motion`)
  },

  /**
   * Set blade screws motion
   * @param {number} drumId - Drum ID
   * @param {Object} motionData - Motion parameters (mode, distance)
   * @returns {Promise} Axios response promise
   */
  setBladeScrewsMotion(drumId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/blade/screws/motion`, motionData)
  },

  /**
   * Cancel blade screws motion
   * @param {number} drumId - Drum ID
   * @returns {Promise} Axios response promise
   */
  cancelBladeScrewsMotion(drumId) {
    return apiClient.delete(`/recoater/drums/${drumId}/blade/screws/motion`)
  },

  /**
   * Get individual blade screw info
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrew(drumId, screwId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/${screwId}`)
  },

  /**
   * Get individual blade screw motion status
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  getBladeScrewMotion(drumId, screwId) {
    return apiClient.get(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`)
  },

  /**
   * Set individual blade screw motion
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @param {Object} motionData - Motion parameters (distance)
   * @returns {Promise} Axios response promise
   */
  setBladeScrewMotion(drumId, screwId, motionData) {
    return apiClient.post(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`, motionData)
  },

  /**
   * Cancel individual blade screw motion
   * @param {number} drumId - Drum ID
   * @param {number} screwId - Screw ID
   * @returns {Promise} Axios response promise
   */
  cancelBladeScrewMotion(drumId, screwId) {
    return apiClient.delete(`/recoater/drums/${drumId}/blade/screws/${screwId}/motion`)
  },

  // Leveler Control API Methods

  /**
   * Get leveler pressure information
   * @returns {Promise} Axios response promise
   */
  getLevelerPressure() {
    return apiClient.get('/recoater/leveler/pressure')
  },

  /**
   * Set leveler pressure target
   * @param {number} target - Target pressure in Pa
   * @returns {Promise} Axios response promise
   */
  setLevelerPressure(target) {
    return apiClient.put('/recoater/leveler/pressure', { target })
  },

  /**
   * Get leveler sensor state
   * @returns {Promise} Axios response promise
   */
  getLevelerSensor() {
    return apiClient.get('/recoater/leveler/sensor')
  },

  // Print Control API Methods

  /**
   * Get layer parameters
   * @returns {Promise} Axios response promise
   */
  getLayerParameters() {
    return apiClient.get('/print/layer/parameters')
  },

  /**
   * Set layer parameters
   * @param {Object} parameters - Layer parameters (filling_id, speed, powder_saving, x_offset)
   * @returns {Promise} Axios response promise
   */
  setLayerParameters(parameters) {
    return apiClient.put('/print/layer/parameters', parameters)
  },

  /**
   * Get layer preview image
   * @returns {Promise} Axios response promise with image data
   */
  getLayerPreview() {
    return apiClient.get('/print/layer/preview', {
      responseType: 'blob'
    })
  },

  /**
   * Start print job
   * @returns {Promise} Axios response promise
   */
  startPrintJob() {
    return apiClient.post('/print/job')
  },

  /**
   * Cancel print job
   * @returns {Promise} Axios response promise
   */
  cancelPrintJob() {
    return apiClient.delete('/print/job')
  }
}

export default apiService
